//
//  ScratchCardView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡主视图
 * 集成所有组件并实现完整的刮刮卡界面
 */
struct ScratchCardView: View {
    
    // MARK: - Properties
    let member: Member
    let onDismiss: () -> Void
    let onNavigateToSettings: () -> Void
    
    // MARK: - Dependencies
    @EnvironmentObject private var dataManager: DataManager
    
    // MARK: - State Properties
    @StateObject private var viewModel: ScratchCardViewModel
    @State private var showBackAlert = false
    
    // MARK: - Initialization
    
    init(member: Member, onDismiss: @escaping () -> Void, onNavigateToSettings: @escaping () -> Void) {
        self.member = member
        self.onDismiss = onDismiss
        self.onNavigateToSettings = onNavigateToSettings
        self._viewModel = StateObject(wrappedValue: ScratchCardViewModel(member: member, dataManager: DataManager.shared))
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变
                backgroundGradient
                
                // 主要内容
                mainContent(geometry: geometry)
                
                // 刮除覆盖层
                if viewModel.showScratchOverlay, let selectedIndex = viewModel.selectedCardIndex {
                    scratchOverlay(cardIndex: selectedIndex)
                        .allowsHitTesting(true) // 禁用点击外面退回
                }
                
                // 结果弹窗
                if viewModel.showResult {
                    resultOverlay
                }
                
                // 积分不足提示
                if viewModel.showInsufficientPoints {
                    insufficientPointsAlert
                }
                
                // 粒子效果层
                particleEffectLayer
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .navigationTitle("scratch_card.title".localized)
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                backButton
            }
        }
        .onAppear {
            viewModel.loadScratchCardConfig()
        }
        .environmentObject(dataManager)
    }
    
    // MARK: - Background Gradient
    
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                Color(hex: "#ff6b6b"),
                Color(hex: "#a9d051")
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    // MARK: - Main Content
    
    private func mainContent(geometry: GeometryProxy) -> some View {
        ScrollView {
            VStack(spacing: 24) {
                // 顶部信息区域
                headerSection
                
                // 内容区域
                if viewModel.isLoading {
                    loadingView
                } else if viewModel.showNoConfig {
                    noConfigView
                } else {
                    scratchCardGrid
                }
                
                Spacer(minLength: 100)
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            // 标题
            Text("scratch_card.happy_title".localized)
                .font(.system(size: 30, weight: .bold))
                .foregroundColor(.white)
                .shadow(color: Color.black.opacity(0.3), radius: 2, x: 0, y: 1)

            Text("scratch_card.happy_subtitle".localized)
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(.white.opacity(0.8))
            
            // 积分信息
            pointsInfoView
        }
    }
    
    // MARK: - Points Info View
    
    private var pointsInfoView: some View {
        HStack(spacing: 20) {
            // 剩余卡片
            VStack(spacing: 4) {
                Image(systemName: "rectangle.stack.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                Text(String(format: "scratch_card.remaining_format".localized, viewModel.unscatchedCount))
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.white.opacity(0.9))
            }
            
            // 每张消耗
            VStack(spacing: 4) {
                Image(systemName: "star.circle.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                Text(String(format: "scratch_card.cost_format".localized, viewModel.costPerScratch))
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.white.opacity(0.9))
            }
            
            // 当前积分
            VStack(spacing: 4) {
                Image(systemName: "star.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(viewModel.canAffordScratching ? Color.green : Color.red)
                
                Text(String(format: "scratch_card.current_points_format".localized, member.currentPoints))
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.white.opacity(0.9))

                Text(viewModel.canAffordScratching ? "scratch_card.points_sufficient".localized : "scratch_card.points_insufficient".localized)
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(viewModel.canAffordScratching ? Color.green : Color.red)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.2))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Loading View
    
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.5)
            
            Text("scratch_card.loading".localized)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white.opacity(0.8))
        }
        .frame(height: 200)
    }
    
    // MARK: - No Config View
    
    private var noConfigView: some View {
        VStack(spacing: 20) {
            Image(systemName: "rectangle.stack.badge.plus")
                .font(.system(size: 60, weight: .light))
                .foregroundColor(.white.opacity(0.6))
            
            VStack(spacing: 8) {
                Text("scratch_card.no_config".localized)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white)

                Text("scratch_card.config_description".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.7))
                    .multilineTextAlignment(.center)
            }

            Button(action: onNavigateToSettings) {
                Text("scratch_card.go_settings".localized)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(Color(hex: "#ff6b6b"))
                    .frame(width: 120, height: 44)
                    .background(Color.white)
                    .clipShape(RoundedRectangle(cornerRadius: 22))
            }
        }
        .frame(height: 300)
    }
    
    // MARK: - Scratch Card Grid
    
    private var scratchCardGrid: some View {
        VStack(spacing: 20) {
            ScratchCardGridView(
                cardItems: viewModel.cardItems,
                onCardTapped: { index in
                    let success = viewModel.selectCard(at: index)
                    if success {
                        // 触觉反馈
                        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                        impactFeedback.impactOccurred()
                    }
                }
            )
        }
    }
    
    // MARK: - Back Button
    
    private var backButton: some View {
        Button(action: {
            onDismiss()
        }) {
            HStack(spacing: 4) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .semibold))
                Text("scratch_card.back".localized)
                    .font(.system(size: 16, weight: .medium))
            }
            .foregroundColor(.white)
        }
    }
    
    // MARK: - Scratch Overlay
    
    private func scratchOverlay(cardIndex: Int) -> some View {
        ZStack {
            // 背景遮罩
            Color.black.opacity(0.8)
                .ignoresSafeArea()
            
            VStack(spacing: 20) {
                // 添加"开心刮刮卡"标题
                VStack(spacing: 20) {
                    Text("scratch_card.happy_title".localized)
                        .font(.system(size: 30, weight: .bold))
                        .foregroundColor(.white)
                        .shadow(color: Color.black.opacity(0.3), radius: 2, x: 0, y: 1)

                    Text("scratch_card.happy_subtitle".localized)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
                .padding(.top, 80)
                
                // 刮刮卡（缩小尺寸）
                ScratchCardCanvasView(
                    cardItem: viewModel.cardItems[cardIndex],
                    onProgressUpdate: { progress in
                        viewModel.updateScratchProgress(index: cardIndex, progress: progress)
                    },
                    onScratchComplete: {
                        viewModel.revealPrize(at: cardIndex)
                    }
                )
                .scaleEffect(1.2)
                
                Spacer()
            }
        }
    }
    
    // MARK: - Result Overlay
    
    private var resultOverlay: some View {
        ScratchCardResultView(
            prizeName: viewModel.resultPrize,
            costPoints: viewModel.costPerScratch,
            onConfirm: {
                viewModel.confirmResult()
            }
        )
    }
    
    // MARK: - Insufficient Points Alert
    
    private var insufficientPointsAlert: some View {
        ZStack {
            Color.black.opacity(0.6)
                .ignoresSafeArea()
            
            VStack(spacing: 20) {
                Text("scratch_card.insufficient_points_title".localized)
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text(String(format: "scratch_card.insufficient_points_message_format".localized, viewModel.costPerScratch, member.currentPoints))
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)

                Button(action: {
                    viewModel.dismissInsufficientPointsAlert()
                }) {
                    Text("common.button.confirm".localized)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(width: 100, height: 44)
                        .background(Color(hex: "#ff6b6b"))
                        .clipShape(RoundedRectangle(cornerRadius: 22))
                }
            }
            .padding(30)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
            )
            .padding(.horizontal, 40)
        }
    }
    
    // MARK: - Particle Effect Layer
    
    private var particleEffectLayer: some View {
        ZStack {
            ForEach(viewModel.particles) { particle in
                Circle()
                    .fill(particle.color)
                    .frame(width: particle.size, height: particle.size)
                    .position(particle.position)
                    .opacity(particle.life)
            }
        }
        .allowsHitTesting(false)
    }
}

#Preview {
    NavigationView {
        ScratchCardView(
            member: Member(), // 需要提供一个示例Member
            onDismiss: {
                print("Dismissed")
            },
            onNavigateToSettings: {
                print("Navigate to settings")
            }
        )
    }
    .environmentObject(DataManager.shared)
}
