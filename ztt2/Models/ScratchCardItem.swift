//
//  ScratchCardItem.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 刮刮卡项目数据模型
 * 用于管理单张刮刮卡的状态、动画和刮除进度
 */
struct ScratchCardItem: Identifiable {
    
    // MARK: - Identifiable
    let id = UUID()
    
    // MARK: - Basic Properties
    let index: Int
    let prizeName: String
    
    // MARK: - State Properties
    var isScratched: Bool = false
    var scratchProgress: Double = 0.0
    var scratchedPath: Path = Path()
    
    // MARK: - Animation Properties
    var position: CGPoint = .zero
    var animationState: ScratchCardAnimationState = .idle
    var scaleEffect: CGFloat = 1.0
    var isSelected: Bool = false
    var opacity: Double = 1.0
    
    // MARK: - Visual Properties
    var cardSkin: ScratchCardSkin = .silver
    
    // MARK: - Computed Properties
    
    /**
     * 获取刮刮卡显示标题
     */
    var displayTitle: String {
        return "刮刮卡 \(index + 1)"
    }
    
    /**
     * 检查是否可以点击
     */
    var isClickable: Bool {
        return !isScratched && animationState == .idle
    }
    
    /**
     * 检查是否已经显示奖品
     */
    var isPrizeRevealed: Bool {
        return scratchProgress >= 0.4
    }
}

/**
 * 刮刮卡动画状态枚举
 */
enum ScratchCardAnimationState: CaseIterable {
    case idle        // 静止状态
    case selected    // 选中状态
    case scratching  // 刮除进行中
    case revealing   // 奖品显示中
    case completed   // 完成状态
    
    var description: String {
        switch self {
        case .idle:
            return "静止"
        case .selected:
            return "选中"
        case .scratching:
            return "刮除中"
        case .revealing:
            return "显示奖品"
        case .completed:
            return "完成"
        }
    }
}

/**
 * 刮刮卡皮肤类型枚举
 */
enum ScratchCardSkin: String, CaseIterable {
    case silver = "silver"
    case gold = "gold"
    case rainbow = "rainbow"
    
    var displayName: String {
        switch self {
        case .silver:
            return "银色"
        case .gold:
            return "金色"
        case .rainbow:
            return "彩虹"
        }
    }
    
    /**
     * 获取背景渐变
     */
    var backgroundGradient: LinearGradient {
        switch self {
        case .silver:
            return LinearGradient(
                colors: [Color.gray.opacity(0.2), Color.gray.opacity(0.1)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        case .gold:
            return LinearGradient(
                colors: [Color.yellow.opacity(0.2), Color.orange.opacity(0.1)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        case .rainbow:
            return LinearGradient(
                colors: [Color.purple.opacity(0.2), Color.pink.opacity(0.1), Color.blue.opacity(0.1)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }
    
    /**
     * 获取遮挡层渐变
     */
    var coverGradient: LinearGradient {
        switch self {
        case .silver:
            return LinearGradient(
                colors: [Color.gray.opacity(0.9), Color.gray.opacity(0.7)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        case .gold:
            return LinearGradient(
                colors: [Color.yellow.opacity(0.9), Color.orange.opacity(0.7)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        case .rainbow:
            return LinearGradient(
                colors: [Color.purple.opacity(0.9), Color.pink.opacity(0.7), Color.blue.opacity(0.8)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }
    
    /**
     * 获取刮除纹理图片（可选）
     */
    var textureImage: String? {
        switch self {
        case .silver:
            return nil
        case .gold:
            return "scratchcard_gold_texture"
        case .rainbow:
            return "scratchcard_rainbow_texture"
        }
    }
}

// MARK: - 扩展方法

extension ScratchCardItem {
    
    /**
     * 创建新的刮刮卡项目
     */
    static func create(index: Int, prizeName: String, skin: ScratchCardSkin = .silver) -> ScratchCardItem {
        return ScratchCardItem(
            index: index,
            prizeName: prizeName,
            cardSkin: skin
        )
    }
    
    /**
     * 开始刮除动画
     */
    mutating func startScratching() {
        guard isClickable else { return }
        animationState = .scratching
        isSelected = true
    }
    
    /**
     * 更新刮除进度
     */
    mutating func updateScratchProgress(_ progress: Double) {
        scratchProgress = min(1.0, max(0.0, progress))
        
        // 当刮除超过阈值时，触发奖品显示
        if scratchProgress >= 0.4 && animationState == .scratching {
            animationState = .revealing
        }
    }
    
    /**
     * 完成刮除
     */
    mutating func completeScratching() {
        isScratched = true
        animationState = .completed
        scratchProgress = 1.0
    }
    
    /**
     * 重置卡片状态
     */
    mutating func reset() {
        isScratched = false
        scratchProgress = 0.0
        scratchedPath = Path()
        animationState = .idle
        isSelected = false
        scaleEffect = 1.0
        opacity = 1.0
    }
}

/**
 * 粒子效果项目
 */
struct ParticleItem: Identifiable {
    let id = UUID()
    var position: CGPoint
    var velocity: CGPoint
    var life: Double = 1.0
    var color: Color
    var size: CGFloat
    
    static func create(at position: CGPoint) -> ParticleItem {
        return ParticleItem(
            position: position,
            velocity: CGPoint(
                x: Double.random(in: -50...50),
                y: Double.random(in: -100...0)
            ),
            color: [Color.yellow, Color.orange, Color.red, Color.green, Color.blue].randomElement() ?? Color.yellow,
            size: CGFloat.random(in: 2...6)
        )
    }
}
